{"name": "college-management-student", "version": "1.0.0", "description": "Student Frontend for College Management System", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "axios": "^1.5.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "react-hook-form": "^7.45.0", "react-query": "^3.39.0", "react-router-dom": "^6.15.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@types/react-router-dom": "^5.3.0"}}