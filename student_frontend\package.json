{"name": "college-management-student", "version": "1.0.0", "description": "Student Frontend for College Management System", "type": "module", "private": true, "scripts": {"dev": "vite --port 3002", "build": "tsc && vite build", "preview": "vite preview --port 3002", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.5.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "react-hook-form": "^7.45.0", "@tanstack/react-query": "^4.35.0", "react-router-dom": "^6.15.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.0", "clsx": "^2.0.0", "lucide-react": "^0.284.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10"}}