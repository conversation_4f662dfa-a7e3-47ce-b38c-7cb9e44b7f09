import { useState, useEffect } from 'react'
import { User } from '@/types'
import { authService } from '@/services/authService'

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
}

export const useAuthStore = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    isAuthenticated: false,
  })
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initAuth = async () => {
      if (isInitialized) return

      try {
        const token = localStorage.getItem('token')
        console.log('Auth initialization - token found:', !!token)
        if (token) {
          console.log('Attempting to get current user...')
          const user = await authService.getCurrentUser()
          console.log('Current user retrieved:', user)
          setAuthState({
            user,
            token,
            isLoading: false,
            isAuthenticated: true,
          })
        } else {
          console.log('No token found, setting unauthenticated state')
          setAuthState({
            user: null,
            token: null,
            isLoading: false,
            isAuthenticated: false,
          })
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        localStorage.removeItem('token')
        setAuthState({
          user: null,
          token: null,
          isLoading: false,
          isAuthenticated: false,
        })
      } finally {
        setIsInitialized(true)
      }
    }

    initAuth()
  }, [isInitialized])

  const login = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true }))
    try {
      const response = await authService.login(email, password)
      console.log('Full login response:', response)

      // Response should now be { user, token } directly from authService
      const { user, token } = response

      if (!user || !token) {
        console.error('Invalid response structure:', response)
        throw new Error('Invalid login response')
      }

      localStorage.setItem('token', token)
      console.log('Setting auth state after login:', { user, token })

      // Set the auth state and mark as initialized to prevent useEffect from overriding
      setAuthState({
        user,
        token,
        isLoading: false,
        isAuthenticated: true,
      })
      setIsInitialized(true)

      console.log('Auth state set, user should be logged in')
      return response
    } catch (error) {
      console.error('Login error:', error)
      setAuthState(prev => ({ ...prev, isLoading: false }))
      throw error
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    setAuthState({
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,
    })
  }

  const updateUser = (updatedUser: User) => {
    setAuthState(prev => ({
      ...prev,
      user: updatedUser,
    }))
  }

  return {
    ...authState,
    login,
    logout,
    updateUser,
  }
}
