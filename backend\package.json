{"name": "college-management-backend", "version": "1.0.0", "description": "Backend API for College Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["college", "management", "api", "education"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}}