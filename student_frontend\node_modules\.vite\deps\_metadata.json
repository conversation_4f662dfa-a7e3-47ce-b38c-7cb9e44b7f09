{"hash": "4fed8ffc", "browserHash": "332965e1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "729fcc3f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b142e6fb", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8a949d1f", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "94e4d0b4", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "d61fab59", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "5694d302", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/lib/index.mjs", "file": "@tanstack_react-query.js", "fileHash": "fa55dd1a", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "93cce8ec", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1082686f", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "6e8a1ab7", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9e4074c7", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "d3dc66b5", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "49a78c49", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "b5a9e82f", "needsInterop": false}}, "chunks": {"chunk-LAV6FB6A": {"file": "chunk-LAV6FB6A.js"}, "chunk-K5YNGTCN": {"file": "chunk-K5YNGTCN.js"}, "chunk-ZC22LKFR": {"file": "chunk-ZC22LKFR.js"}}}