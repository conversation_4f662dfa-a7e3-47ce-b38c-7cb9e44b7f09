import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import authSlice from './slices/authSlice'
import courseSlice from './slices/courseSlice'
import gradeSlice from './slices/gradeSlice'
import paymentSlice from './slices/paymentSlice'
import registrationSlice from './slices/registrationSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    courses: courseSlice,
    grades: gradeSlice,
    payments: paymentSlice,
    registration: registrationSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector

export default store
