# Database Configuration
MONGODB_URI=mongodb://localhost:27017/college_management
DB_NAME=college_management

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
FRONTEND_ADMIN_URL=http://localhost:3001
FRONTEND_STUDENT_URL=http://localhost:3002

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
