# Dependencies
node_modules/
*/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*/.env
*/.env.local
*/.env.development.local
*/.env.test.local
*/.env.production.local

# Build outputs
build/
dist/
*/build/
*/dist/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*/coverage/
*.lcov

# nyc test coverage
.nyc_output
*/.nyc_output

# Cache directories
.cache/
.parcel-cache/
.vite/
*/.cache/
*/.parcel-cache/
*/.vite/

# Optional npm cache directory
.npm
*/.npm

# Optional eslint cache
.eslintcache
*/.eslintcache

# TypeScript cache
*.tsbuildinfo

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Temporary folders
tmp/
temp/
*/tmp/
*/temp/

# File uploads
uploads/
*/uploads/
public/uploads/
*/public/uploads/

# Database files
*.db
*.sqlite
*.sqlite3

# Test results
test-results/
*/test-results/
playwright-report/
*/playwright-report/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE specific files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
